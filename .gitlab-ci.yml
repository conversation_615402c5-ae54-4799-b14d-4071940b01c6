# .gitlab-ci.yml

# Define the stages of the pipeline
stages:
  # - setup_image_repo
  - tests_nightly_run_dev
  - build_services
  - publish

default:
  tags:
    # This tell Gitlab to use our newly created runners.
    # this sould correspond to runner_tags config/app.yml above
    - autoscaling

services:
  - name: public.ecr.aws/docker/library/docker:dind
    command: [ "--tls=false" ]

variables:
  INT_MIDDLEWARE_DEPLOYMENT_ID: $DEV_DEPLOYMENT_ID
  ENV_ID: "QA"
  DOCKER_REGISTRY: 059791169198.dkr.ecr.us-east-1.amazonaws.com
  DOCKER_HOST: "tcp://docker:2375"
  DOCKER_TLS_CERTDIR: ""
  DOCKER_DRIVER: overlay2
  KUBERNETES_CPU_REQUEST: "2"
  KUBERNETES_CPU_LIMIT: "2"
  KUBERNETES_MEMORY_REQUEST: "7.5Gi"
  KUBERNETES_MEMORY_LIMIT: "7.5Gi"

cache:
  paths:
    - .gradle/wrapper
    - .gradle/caches

build_services_qa:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: build_services
  only:
    - dev
  tags:
    - autoscaling
  artifacts:
    paths:
      - admin/webservice/build/libs/*.jar
      - integrations/auth/webservice/build/libs/*.jar
      - integrations/core/webservice/build/libs/*.jar
      - integrations/custom/workday/webservice/build/libs/*.jar
      - integrations/custom/peoplesoft/webservice/build/libs/*.jar

  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew admin:admin-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:auth:int-auth-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:core:int-core-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:workday:int-custom-workday-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:peoplesoft:int-custom-peoplesoft-webservice:build -x test
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/
  except:
    variables:
      - $NIGHTLY_TEST

build_services_sit:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: build_services
  tags:
    - autoscaling
  artifacts:
    paths:
      - admin/webservice/build/libs/*.jar
      - integrations/auth/webservice/build/libs/*.jar
      - integrations/core/webservice/build/libs/*.jar
      - integrations/custom/workday/webservice/build/libs/*.jar
      - integrations/custom/peoplesoft/webservice/build/libs/*.jar

  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew admin:admin-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:auth:int-auth-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:core:int-core-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:workday:int-custom-workday-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:peoplesoft:int-custom-peoplesoft-webservice:build -x test
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/
  rules:
    - if: '$NIGHTLY_TEST == "True"'
      when: never
    - if: '$CI_COMMIT_REF_NAME == $SIT_BRANCH_NAME && $CI_PIPELINE_SOURCE != "merge_request_event"'
      when: always

workday_integration_tests_sit:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: tests_nightly_run_dev
  tags:
    - autoscaling
  artifacts:
    when: always
    reports:
      junit: "integrations/custom/workday/webservice/build/test-results/**/TEST-*.xml"
  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew integrations:custom:workday:int-custom-workday-webservice:integrationTest
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" && $NIGHTLY_TEST == "True"'
      when: always
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" && $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always

peoplesoft_destiny_custom_tmu_tests_sit:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: tests_nightly_run_dev
  tags:
    - autoscaling
  artifacts:
    when: always
    reports:
      junit: "integrations/custom/peoplesoft/destiny-tmu-webservice/build/test-results/**/TEST-*.xml"
  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew integrations:custom:peoplesoft:int-custom-peoplesoft-destiny-tmu-webservice:integrationTest
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" && $NIGHTLY_TEST == "True"'
      when: always
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" && $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always

core_integration_tests_sit:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: tests_nightly_run_dev
  tags:
    - autoscaling
  artifacts:
    when: always
    reports:
      junit: "integrations/core/webservice/build/test-results/**/TEST-*.xml"
  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew integrations:core:int-core-webservice:integrationTest
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" && $NIGHTLY_TEST == "True"'
      when: always
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" && $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always

build_services_test:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: build_services
  cache: [ ]
  only:
    - prod
  tags:
    - autoscaling
  artifacts:
    paths:
      - admin/webservice/build/libs/*.jar
      - integrations/core/webservice/build/libs/*.jar
      - integrations/custom/workday/webservice/build/libs/*.jar
      - integrations/custom/peoplesoft/webservice/build/libs/*.jar

  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew admin:admin-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:core:int-core-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:workday:int-custom-workday-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:peoplesoft:int-custom-peoplesoft-webservice:build -x test
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/

build_services_prod:
  image: public.ecr.aws/docker/library/eclipse-temurin:21-jdk-alpine
  stage: build_services
  cache: [ ]
  only:
    - tags
  tags:
    - autoscaling
  artifacts:
    paths:
      - admin/webservice/build/libs/*.jar
      - integrations/custom/workday/webservice/build/libs/*.jar
      - integrations/custom/peoplesoft/webservice/build/libs/*.jar

  script:
    - export GRADLE_USER_HOME=`pwd`/.gradle
    - chmod +x gradlew && ./gradlew admin:admin-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:workday:int-custom-workday-webservice:build -x test
    - chmod +x gradlew && ./gradlew integrations:custom:peoplesoft:int-custom-peoplesoft-webservice:build -x test
    - rm -f .gradle/caches/modules-2/modules-2.lock
    - rm -f .gradle/caches/*/plugin-resolution/

publish_qa:
  dependencies:
    - build_services_qa
  stage: publish
  only:
    - dev
  variables:
    ADMIN_APP_NAME: valence-admin
    WORKDAY_APP_NAME: valence-workday
    PEOPLESOFT_APP_NAME: valence-peoplesoft
    CORE_APP_NAME: valence-uapi
    AUTH_APP_NAME: valence-auth
  tags:
    - autoscaling
  image:
    name: 059791169198.dkr.ecr.us-east-1.amazonaws.com/aws-cli:latest
    entrypoint: [ "" ]
  services:
    - docker:dind
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
    - yum install -y jq
    - jq --version
    - ARGOCD_VERSION=v2.5.9
    - curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/download/$ARGOCD_VERSION/argocd-linux-amd64
    - chmod +x /usr/local/bin/argocd
  script:
    # AWS Assume IAM Role
    - CREDENTIALS=$(aws sts assume-role --role-arn "arn:aws:iam::059791169198:role/nucleus_ci" --role-session-name nucleus-runner)
    - export AWS_ACCESS_KEY_ID="$(echo $CREDENTIALS | jq -r .Credentials.AccessKeyId)"
    - export AWS_SECRET_ACCESS_KEY="$(echo $CREDENTIALS | jq -r .Credentials.SecretAccessKey)"
    - export AWS_SESSION_TOKEN="$(echo $CREDENTIALS | jq -r .Credentials.SessionToken)"
    - aws sts get-caller-identity
    # ADMIN
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - cd admin/webservice
    - docker pull $DOCKER_REGISTRY/$ADMIN_APP_NAME:$QA_BRANCH_TAG || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$ADMIN_APP_NAME:$QA_BRANCH_TAG --tag  $DOCKER_REGISTRY/$ADMIN_APP_NAME:$QA_BRANCH_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$ADMIN_APP_NAME:$QA_BRANCH_TAG
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-qa restart --kind Deployment --resource-name admin --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

    # AUTH
    - cd ../..
    - cd integrations/auth/webservice
    - docker pull $DOCKER_REGISTRY/$AUTH_APP_NAME:$QA_BRANCH_TAG || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$AUTH_APP_NAME:$QA_BRANCH_TAG --tag  $DOCKER_REGISTRY/$AUTH_APP_NAME:$QA_BRANCH_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$AUTH_APP_NAME:$QA_BRANCH_TAG
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-qa restart --kind Deployment --resource-name auth --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ..

    # WORKDAY
    - cd ../..
    - cd integrations/custom/workday/webservice
    - docker pull $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$QA_BRANCH_TAG || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$QA_BRANCH_TAG --tag  $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$QA_BRANCH_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$QA_BRANCH_TAG
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-qa restart --kind Deployment --resource-name workday --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # PEOPLESOFT
    - cd ../..
    - cd integrations/custom/peoplesoft/webservice
    - docker pull $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$QA_BRANCH_TAG || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$QA_BRANCH_TAG --tag  $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$QA_BRANCH_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$QA_BRANCH_TAG
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-qa restart --kind Deployment --resource-name peoplesoft --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # UAPI
    - cd ../..
    - cd integrations/core/webservice
    - docker pull $DOCKER_REGISTRY/$CORE_APP_NAME:$QA_BRANCH_TAG || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$CORE_APP_NAME:$QA_BRANCH_TAG --tag  $DOCKER_REGISTRY/$CORE_APP_NAME:$QA_BRANCH_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$CORE_APP_NAME:$QA_BRANCH_TAG
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-qa restart --kind Deployment --resource-name uapi --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

    # Perform health check on ArgoCD app to verify all services came up healthy.
    - argocd app wait valence-qa --health --timeout 300 --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
  except:
    variables:
      - $NIGHTLY_TEST

publish_sit:
  dependencies:
    - build_services_sit
  stage: publish
  variables:
    ADMIN_APP_NAME: valence-admin
    WORKDAY_APP_NAME: valence-workday
    PEOPLESOFT_APP_NAME: valence-peoplesoft
    CORE_APP_NAME: valence-uapi
    AUTH_APP_NAME: valence-auth
  tags:
    - autoscaling
  image:
    name: 059791169198.dkr.ecr.us-east-1.amazonaws.com/aws-cli:latest
    entrypoint: [ "" ]
  services:
    - docker:dind
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
    - yum install -y jq
    - jq --version
    - ARGOCD_VERSION=v2.5.9
    - curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/download/$ARGOCD_VERSION/argocd-linux-amd64
    - chmod +x /usr/local/bin/argocd
  script:
    # AWS Assume IAM Role
    - CREDENTIALS=$(aws sts assume-role --role-arn "arn:aws:iam::059791169198:role/nucleus_ci" --role-session-name nucleus-runner)
    - export AWS_ACCESS_KEY_ID="$(echo $CREDENTIALS | jq -r .Credentials.AccessKeyId)"
    - export AWS_SECRET_ACCESS_KEY="$(echo $CREDENTIALS | jq -r .Credentials.SecretAccessKey)"
    - export AWS_SESSION_TOKEN="$(echo $CREDENTIALS | jq -r .Credentials.SessionToken)"
    - aws sts get-caller-identity
    # ADMIN
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - cd admin/webservice
    - docker pull $DOCKER_REGISTRY/$ADMIN_APP_NAME:dev || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$ADMIN_APP_NAME:dev --tag  $DOCKER_REGISTRY/$ADMIN_APP_NAME:dev .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$ADMIN_APP_NAME:dev
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-sit restart --kind Deployment --resource-name admin --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

    # AUTH
    - cd ../..
    - cd integrations/auth/webservice
    - docker pull $DOCKER_REGISTRY/$AUTH_APP_NAME:dev || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$AUTH_APP_NAME:dev --tag  $DOCKER_REGISTRY/$AUTH_APP_NAME:dev .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$AUTH_APP_NAME:dev
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-sit restart --kind Deployment --resource-name auth --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ..

    # WORKDAY
    - cd ../..
    - cd integrations/custom/workday/webservice
    - docker pull $DOCKER_REGISTRY/$WORKDAY_APP_NAME:dev || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$WORKDAY_APP_NAME:dev --tag  $DOCKER_REGISTRY/$WORKDAY_APP_NAME:dev .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$WORKDAY_APP_NAME:dev
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-sit restart --kind Deployment --resource-name workday --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # PEOPLESOFT
    - cd ../..
    - cd integrations/custom/peoplesoft/webservice
    - docker pull $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:dev || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:dev --tag  $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:dev .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:dev
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-sit restart --kind Deployment --resource-name peoplesoft --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # UAPI
    - cd ../..
    - cd integrations/core/webservice
    - docker pull $DOCKER_REGISTRY/$CORE_APP_NAME:dev || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$CORE_APP_NAME:dev --tag  $DOCKER_REGISTRY/$CORE_APP_NAME:dev .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$CORE_APP_NAME:dev
    # Use ArgoCD to restart the k8s deployment on remote SIT.
    - argocd app actions run valence-sit restart --kind Deployment --resource-name uapi --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

    # Perform health check on ArgoCD app to verify all services came up healthy.
    - argocd app wait valence-sit --health --timeout 300 --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
  rules:
    - if: '$NIGHTLY_TEST == "True"'
      when: never
    - if: '$CI_COMMIT_REF_NAME == $SIT_BRANCH_NAME && $CI_PIPELINE_SOURCE != "merge_request_event"'
      when: always

publish_test:
  dependencies:
    - build_services_test
  stage: publish
  only:
    - prod
  variables:
    ADMIN_APP_NAME: valence-admin
    WORKDAY_APP_NAME: valence-workday
    PEOPLESOFT_APP_NAME: valence-peoplesoft
    CORE_APP_NAME: valence-uapi
  tags:
    - autoscaling
  image:
    name: 059791169198.dkr.ecr.us-east-1.amazonaws.com/aws-cli:latest
    entrypoint: [ "" ]
  services:
    - docker:dind
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
    - yum install -y jq
    - jq --version
    - ARGOCD_VERSION=v2.5.9
    - curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/download/$ARGOCD_VERSION/argocd-linux-amd64
    - chmod +x /usr/local/bin/argocd
  script:
    # AWS Assume IAM Role
    - CREDENTIALS=$(aws sts assume-role --role-arn "arn:aws:iam::059791169198:role/nucleus_ci" --role-session-name nucleus-runner)
    - export AWS_ACCESS_KEY_ID="$(echo $CREDENTIALS | jq -r .Credentials.AccessKeyId)"
    - export AWS_SECRET_ACCESS_KEY="$(echo $CREDENTIALS | jq -r .Credentials.SecretAccessKey)"
    - export AWS_SESSION_TOKEN="$(echo $CREDENTIALS | jq -r .Credentials.SessionToken)"
    - aws sts get-caller-identity
    # ADMIN
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - cd admin/webservice
    - docker pull $DOCKER_REGISTRY/$ADMIN_APP_NAME:prod || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$ADMIN_APP_NAME:prod --tag  $DOCKER_REGISTRY/$ADMIN_APP_NAME:prod .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$ADMIN_APP_NAME:prod
    # Use ArgoCD to restart the k8s deployment on remote Test.
    - argocd app actions run valence-test restart --kind Deployment --resource-name admin --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

    # WORKDAY
    - cd ../..
    - cd integrations/custom/workday/webservice
    - docker pull $DOCKER_REGISTRY/$WORKDAY_APP_NAME:prod || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$WORKDAY_APP_NAME:prod --tag  $DOCKER_REGISTRY/$WORKDAY_APP_NAME:prod .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$WORKDAY_APP_NAME:prod
    # Use ArgoCD to restart the k8s deployment on remote Test.
    - argocd app actions run valence-test restart --kind Deployment --resource-name workday --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # PEOPLESOFT
    - cd ../..
    - cd integrations/custom/peoplesoft/webservice
    - docker pull $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:prod || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:prod --tag  $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:prod .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:prod
    # Use ArgoCD to restart the k8s deployment on remote Test.
    - argocd app actions run valence-test restart --kind Deployment --resource-name peoplesoft --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech
    - cd ../..

    # UAPI
    - cd ../..
    - cd integrations/core/webservice
    - docker pull $DOCKER_REGISTRY/$CORE_APP_NAME:prod || true
    - docker build -f src/main/docker/Dockerfile.jvm --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $DOCKER_REGISTRY/$CORE_APP_NAME:prod --tag  $DOCKER_REGISTRY/$CORE_APP_NAME:prod .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$CORE_APP_NAME:prod
    # Use ArgoCD to restart the k8s deployment on remote Test.
    - argocd app actions run valence-test restart --kind Deployment --resource-name uapi --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech


    # Perform health check on ArgoCD app to verify all services came up healthy.
    - argocd app wait valence-test --health --timeout 300 --auth-token $ARGOCD_TOKEN --server da-argocd.qa.digarc.tech

# Only pushes the images to the ECR Repos. Ops to do a controlled deploy into prod
publish_prod:
  dependencies:
    - build_services_prod
  stage: publish
  only:
    - tags
  variables:
    ADMIN_APP_NAME: valence-admin
    WORKDAY_APP_NAME: valence-workday
    PEOPLESOFT_APP_NAME: valence-peoplesoft
    DOCKER_REGISTRY: 093643113471.dkr.ecr.us-east-1.amazonaws.com
  tags:
    - autoscaling
  image:
    name: 059791169198.dkr.ecr.us-east-1.amazonaws.com/aws-cli:latest
    entrypoint: [ "" ]
  services:
    - docker:dind
  before_script:
    - amazon-linux-extras install docker
    - aws --version
    - docker --version
    - yum install -y jq
    - jq --version
    - ARGOCD_VERSION=v2.5.9
    - curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/download/$ARGOCD_VERSION/argocd-linux-amd64
    - chmod +x /usr/local/bin/argocd
  script:
    # AWS Assume IAM Role
    - CREDENTIALS=$(aws sts assume-role --role-arn "arn:aws:iam::093643113471:role/nucleus_ci" --role-session-name nucleus-runner)
    - export AWS_ACCESS_KEY_ID="$(echo $CREDENTIALS | jq -r .Credentials.AccessKeyId)"
    - export AWS_SECRET_ACCESS_KEY="$(echo $CREDENTIALS | jq -r .Credentials.SecretAccessKey)"
    - export AWS_SESSION_TOKEN="$(echo $CREDENTIALS | jq -r .Credentials.SessionToken)"
    - aws sts get-caller-identity
    # ADMIN
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - cd admin/webservice
    - docker build -f src/main/docker/Dockerfile.jvm --tag  $DOCKER_REGISTRY/$ADMIN_APP_NAME:$CI_COMMIT_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$ADMIN_APP_NAME:$CI_COMMIT_TAG

    # WORKDAY
    - cd ../..
    - cd integrations/custom/workday/webservice
    - docker build -f src/main/docker/Dockerfile.jvm --tag  $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$CI_COMMIT_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$WORKDAY_APP_NAME:$CI_COMMIT_TAG
    - cd ../..

    # PEOPLESOFT
    - cd ../..
    - cd integrations/custom/peoplesoft/webservice
    - docker build -f src/main/docker/Dockerfile.jvm --tag  $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$CI_COMMIT_TAG .
    # Push to ECR
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker push $DOCKER_REGISTRY/$PEOPLESOFT_APP_NAME:$CI_COMMIT_TAG

