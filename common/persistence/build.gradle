import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'java-library'
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    implementation "org.springframework:spring-context"

    implementation "org.apache.commons:commons-lang3"
    implementation "commons-io:commons-io:${libCommonsIo}"

    implementation project(':common:common-common')

    api platform("software.amazon.awssdk:bom:${libAWSSDK}")
    api 'software.amazon.awssdk:url-connection-client'
    api "software.amazon.awssdk:dynamodb-enhanced"
    runtimeOnly 'software.amazon.awssdk:sts'

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation "org.junit.jupiter:junit-jupiter:${libJUnitJupiter}"

}