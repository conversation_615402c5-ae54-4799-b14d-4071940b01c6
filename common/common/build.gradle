import org.springframework.boot.gradle.plugin.SpringBootPlugin

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation "org.springframework.boot:spring-boot-starter-test"
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation "org.junit.jupiter:junit-jupiter:${libJUnitJupiter}"

}