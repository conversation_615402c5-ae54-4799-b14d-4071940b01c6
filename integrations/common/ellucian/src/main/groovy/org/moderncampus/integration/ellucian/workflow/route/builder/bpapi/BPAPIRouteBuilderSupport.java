package org.moderncampus.integration.ellucian.workflow.route.builder.bpapi;

import org.moderncampus.integration.ellucian.component.BannerCloudConnectionConfiguration;
import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;
import org.moderncampus.integration.ellucian.component.internal.BannerBPAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudCreateRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudGetByIdRouteBuilder;
import org.moderncampus.integration.ellucian.workflow.route.builder.EllucianCloudUpdateRouteBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;

public class BPAPIRouteBuilderSupport {

    public static EllucianCloudCreateRouteBuilder.EllucianCloudCreateRouteBuilderBuilder createBuilder(String id,
            BannerBPAPIResource apiResource, Object outTransformHelper,
            String outTransformMethod,
            Object inTransformHelper, String inTransformMethod) {
        return EllucianCloudCreateRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_BP_API, apiResource,
                BannerCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, inTransformHelper,
                inTransformMethod);
    }

    public static EllucianCloudUpdateRouteBuilder.EllucianCloudUpdateRouteBuilderBuilder updateByIdBuilder(String id,
            BannerBPAPIResource bpapiResource, Object outTransformHelper,
            String outTransformMethod, ObjectMapper mapper) {
        return EllucianCloudUpdateRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_BP_API, bpapiResource,
                BannerCloudConnectionConfiguration.class, outTransformHelper, outTransformMethod, mapper);
    }


    public static EllucianCloudGetByIdRouteBuilder.EllucianCloudGetByIdRouteBuilderBuilder getByIdBuilder(String id,
            BannerBPAPIResource bpapiResource,
            Object transformer, String transformMethod) {
        return EllucianCloudGetByIdRouteBuilder.builder(id, EllucianCloudEndpointType.BANNER_BP_API, bpapiResource,
                BannerCloudConnectionConfiguration.class, transformer, transformMethod);
    }

}
