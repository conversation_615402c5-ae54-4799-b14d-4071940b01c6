package org.moderncampus.integration.ellucian.component.internal;

import org.moderncampus.integration.ellucian.component.endpoint.EllucianCloudEndpointType;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum BannerEthosAPIResource implements IEllucianCloudAPIResource {

    EDUCATIONAL_INSTITUTION_UNITS("educational-institution-units"),
    SUBJECTS("subjects"),
    INSTRUCTIONAL_METHODS("instructional-methods"),
    SITES("sites"),
    ACADEMIC_LEVELS("academic-levels"),
    ROOMS("rooms"),
    SECTIONS("sections"),
    SECTION_TITLE_TYPES("section-title-types"),
    SECTION_DESCRIPTION_TYPES("section-description-types"),
    ADMINISTRATIVE_INSTRUCTIONAL_METHODS("administrative-instructional-methods"),
    CREDIT_CATEGORIES("credit-categories"),
    SECTION_STATUSES("section-statuses"),
    COURSES("courses"),
    COURSE_TITLE_TYPES("course-title-types"),
    INSTRUCTIONAL_EVENTS("instructional-events"),
    ACADEMIC_PERIODS("academic-periods"),
    INSTRUCTORS("instructors"),
    PERSONS("persons"),
    SECTION_INSTRUCTORS("section-instructors"),
    SECTION_CROSS_LIST_QUERY("schedule-cross-list-query");

    BannerEthosAPIResource(String value) {
        this.value = value;
        this.acceptHeaderVersion = null;
        this.contentTypeHeaderVersion = null;
    }

    String value;
    String acceptHeaderVersion;
    String contentTypeHeaderVersion;


    @Override
    public String getPathSegment() {
        return "/api/" + getValue();
    }

    @Override
    public EllucianCloudEndpointType endpointType() {
        return EllucianCloudEndpointType.BANNER_ETHOS_API;
    }

    @Override
    public boolean sendBodyAsBytes() {
        return false;
    }
}
