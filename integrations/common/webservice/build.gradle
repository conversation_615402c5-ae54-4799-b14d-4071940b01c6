import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

tasks.named("jar") {
    archiveClassifier = ''
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.session:spring-session-core'

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"

    implementation project(':common:common-common')
    implementation project(':common:common-webservice-base')
    implementation project(':integrations:common:int-common-core')
    implementation project(':common:common-tenants')

    implementation "com.google.guava:guava:$libGuava"

    implementation 'org.apache.commons:commons-lang3'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation 'org.springframework.security:spring-security-test'
}