package org.moderncampus.integration.route.identifier;

public class Constants {

    public static final String GET_ORGANIZATIONAL_UNITS = "getOrganizationalUnits";
    public static final String GET_ORGANIZATIONAL_UNIT = "getOrganizationalUnit";
    public static final String GET_SUBJECTS = "getSubjects";
    public static final String GET_SUBJECT = "getSubject";
    public static final String GET_INSTRUCTIONAL_METHODS = "getInstructionalMethods";
    public static final String GET_INSTRUCTIONAL_METHOD = "getInstructionalMethod";
    public static final String GET_LOCATIONS = "getLocations";
    public static final String GET_LOCATION = "getLocation";
    public static final String GET_ACADEMIC_LEVELS = "getAcademicLevels";
    public static final String GET_ACADEMIC_LEVEL = "getAcademicLevel";
    public static final String GET_TERMS = "getTerms";
    public static final String GET_COURSES = "getCourses";
    public static final String GET_SECTIONS = "getSections";
    public static final String GET_ROOMS = "getRooms";
    public static final String GET_ROOM = "getRoom";
    public static final String GET_ACADEMIC_PROGRAMS = "getAcademicPrograms";
    public static final String GET_SECTION = "getSection";
    public static final String CREATE_SECTION = "createSection";
    public static final String UPDATE_SECTION = "updateSection";
    public static final String CREATE_COURSE = "createCourse";
    public static final String UPDATE_COURSE = "updateCourse";
    public static final String GET_COURSE = "getCourse";
    public static final String CREATE_SECTION_SCHEDULE = "createSectionSchedule";
    public static final String UPDATE_SECTION_SCHEDULE = "updateSectionSchedule";
    public static final String GET_SECTION_SCHEDULE = "getSectionSchedule";
    public static final String GET_SECTION_SCHEDULES = "getSectionSchedules";
    public static final String DELETE_SECTION_SCHEDULE = "deleteSectionSchedule";
    public static final String GET_ACADEMIC_PERIODS = "getAcademicPeriods";
    public static final String GET_ACADEMIC_PERIOD = "getAcademicPeriod";
    public static final String GET_INSTRUCTORS = "getInstructors";
    public static final String GET_INSTRUCTOR = "getInstructor";
    public static final String GET_SECTION_INSTRUCTOR_ASSIGNMENT = "getSectionInstructorAssignment";
    public static final String GET_SECTION_INSTRUCTOR_ASSIGNMENTS = "getSectionInstructorAssignments";
    public static final String CREATE_SECTION_INSTRUCTOR_ASSIGNMENT = "createSectionInstructorAssignment";
    public static final String DELETE_SECTION_INSTRUCTOR_ASSIGNMENT = "deleteSectionInstructorAssignment";
    public static final String UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT = "updateSectionInstructorAssignment";
    public static final String CREATE_SECTION_CROSS_LIST = "createSectionCrossList";
    public static final String DELETE_SECTION_CROSS_LIST = "deleteSectionCrossList";
    public static final String GET_SECTION_CROSS_LISTS = "getSectionCrossLists";
    public static final String GET_ADDRESS = "getAddress";
    public static final String CREATE_PERSON_EMERGENCY_CONTACT = "createPersonEmergencyContact";
    public static final String DELETE_PERSON_EMERGENCY_CONTACT = "deletePersonEmergencyContact";
    public static final String CREATE_PERSON = "createPerson";
    public static final String CHECK_DUPLICATE_PERSON = "checkDuplicatePerson";
    public static final String CREATE_STUDENT_CHARGE = "createStudentCharge";
    public static final String CREATE_STUDENT_PAYMENT = "createStudentPayment";
    public static final String CREATE_STUDENT_ENROLLMENT = "createStudentEnrollment";
    public static final String UPDATE_STUDENT_ENROLLMENT = "updateStudentEnrollment";
    public static final String UPDATE_STUDENT = "updateStudent";
    public static final String UPDATE_PERSON = "updatePerson";
    public static final String UPDATE_PERSON_ADDRESS = "updatePersonAddress";
    public static final String CREATE_FINAL_GRADE = "createFinalGrade";
    public static final String UPDATE_FINAL_GRADE = "updateFinalGrade";
    public static final String GET_SECTION_CROSS_LIST_GROUPS = "getSectionCrossListGroups";
    public static final String CREATE_SECTION_CROSS_LIST_GROUP = "createSectionCrossListGroup";
    public static final String UPDATE_SECTION_CROSS_LIST_GROUP = "updateSectionCrossListGroup";
    public static final String UPDATE_COURSE_SECTION_INFORMATION = "updateCourseSectionInformation";
    public static final String GET_COURSE_SECTION_INFORMATION = "getCourseSectionInformation";
    public static final String CREATE_ORGANIZATION = "createOrganization";
    public static final String UPDATE_ORGANIZATION = "updateOrganization";
    public static final String PROXY = "proxy";
}
