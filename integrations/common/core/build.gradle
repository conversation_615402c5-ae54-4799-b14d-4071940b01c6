import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id "groovy"
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    implementation "org.springframework:spring-context"
    implementation enforcedPlatform("org.apache.camel.springboot:camel-spring-boot-bom:${libCamel}")

    implementation project(':common:common-common')
    implementation project(':common:common-persistence')
    implementation project(':common:common-tenants')

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')


    implementation "com.google.guava:guava:$libGuava"

    implementation("org.apache.camel:camel-api:${libCamel}")
    implementation("org.apache.camel:camel-core-model:${libCamel}")
    compileOnly 'org.apache.camel.springboot:camel-http-starter'
    compileOnly 'org.apache.camel.springboot:camel-bean-validator-starter'
    compileOnly 'org.apache.camel.springboot:camel-jaxb-starter'
    compileOnly 'org.apache.camel.springboot:camel-jsonpath-starter'

    compileOnly 'org.springframework.boot:spring-boot-starter-actuator'

    implementation "org.springframework.boot:spring-boot-starter-aop"
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"
    implementation "org.springframework:spring-aop"

    implementation "org.apache.commons:commons-lang3"
    implementation "commons-io:commons-io:${libCommonsIo}"

    implementation 'org.apache.camel.springboot:camel-groovy-starter'
    implementation 'org.apache.groovy:groovy-xml'

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation "org.springframework.boot:spring-boot-starter-test"
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation "org.junit.jupiter:junit-jupiter:${libJUnitJupiter}"
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.assertj:assertj-core'
    testImplementation "org.apache.camel:tests:${libCamel}"

}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

compileGroovy {
    options.encoding = compileJava.options.encoding
    options.compilerArgs.addAll(compileJava.options.compilerArgs)
}