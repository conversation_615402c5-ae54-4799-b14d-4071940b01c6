package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCAddress
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
public class MCPersonAddressToEthosColleaguePersonAddressWriteTransform extends
        BaseTransformer<MCAddress, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCAddress input) {
        Map<String, ?> requestRoot = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        def addressTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.ADDRESS_TYPES)
        return JsonOutput.toJson(createOrUpdateAddress(input, requestRoot, addressTypesById))//currently only supports an update
    }

     Map createOrUpdateAddress(MCAddress personAddr, Map<String, ?> existingPersonAddress = null, Map<String, Map> addressTypesById) {
        def address = [:]
        if (existingPersonAddress != null) {
            address = existingPersonAddress
        }
        if (isValueExist(personAddr.addressLine1) || isValueExist(personAddr.addressLine2 || isValueExist(personAddr.addressLine3) ||
                isValueExist(personAddr.addressLine4))) {
            address.addressLines = [personAddr.addressLine1, personAddr.addressLine2, personAddr.addressLine3, personAddr.addressLine4]
                    .findAll { it != null }
                    .collect { it.trim() }
        }

        if (isValueExist(personAddr.city)) {
            commonWriteTransform.setNestedValue(address, personAddr.city, "place", "country", "locality")
        }

        if (isValueExist(personAddr.state)) {
            commonWriteTransform.setNestedValue(address, personAddr.state.stateCode, "place", "country", "region", "code")
            commonWriteTransform.setNestedValue(address, personAddr.state.stateName, "place", "country", "region", "title")
        }

        if (isValueExist(personAddr.county)) {
            commonWriteTransform.setNestedValue(address, personAddr.county.countyCode, "place", "country", "subRegion", "code")
            commonWriteTransform.setNestedValue(address, personAddr.county.countyName, "place", "country", "subRegion", "title")
        }

        if (isValueExist(personAddr.country)) {
            commonWriteTransform.setNestedValue(address, personAddr.country.countryCode, "place", "country", "code")
            commonWriteTransform.setNestedValue(address, personAddr.country.countryName, "place", "country", "title")
        }

        if (isValueExist(personAddr.postalCode)) {
            commonWriteTransform.setNestedValue(address, personAddr.postalCode, "place", "country", "postalCode")
        }

        return address
    }
}
