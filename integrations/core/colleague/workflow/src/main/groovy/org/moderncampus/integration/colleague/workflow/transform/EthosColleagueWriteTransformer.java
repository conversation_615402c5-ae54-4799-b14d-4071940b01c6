package org.moderncampus.integration.colleague.workflow.transform;

import org.apache.camel.Exchange;
import org.moderncampus.integration.colleague.workflow.transform.course.MCCourseToEthosColleagueCourseWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.person.MCOrganizationToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.person.MCPersonAddressToEthosColleaguePersonAddressWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.person.MCPersonEmergencyContactToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.person.MCPersonToEthosColleagueDupCheckWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.person.MCPersonToEthosColleaguePersonWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionCrossListToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionInstructorToEthosColleagueSectionInstructorWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionScheduleToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.section.MCSectionToEthosColleagueSectionWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.student.MCFinalGradeToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.student.MCStudentChargeToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.student.MCStudentEnrollmentToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.student.MCStudentPaymentToEthosColleagueWriteTransform;
import org.moderncampus.integration.colleague.workflow.transform.student.MCStudentToEthosColleagueWriteTransform;
import org.moderncampus.integration.dto.core.MCAddress;
import org.moderncampus.integration.dto.core.MCCourse;
import org.moderncampus.integration.dto.core.MCFinalGrade;
import org.moderncampus.integration.dto.core.MCOrganization;
import org.moderncampus.integration.dto.core.MCPerson;
import org.moderncampus.integration.dto.core.MCPersonEmergencyContact;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.dto.core.MCSectionCrossList;
import org.moderncampus.integration.dto.core.MCSectionCrossListGroup;
import org.moderncampus.integration.dto.core.MCSectionInstructorAssignment;
import org.moderncampus.integration.dto.core.MCSectionSchedule;
import org.moderncampus.integration.dto.core.MCStudent;
import org.moderncampus.integration.dto.core.MCStudentCharge;
import org.moderncampus.integration.dto.core.MCStudentEnrollment;
import org.moderncampus.integration.dto.core.MCStudentPayment;
import org.moderncampus.integration.ellucian.workflow.transform.helper.EthosHelper;
import org.moderncampus.integration.transform.IExtSystemWriteTransformer;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosColleagueWriteTransformer implements IExtSystemWriteTransformer {

    MCCourseToEthosColleagueCourseWriteTransform courseWriteTransform;
    MCSectionToEthosColleagueSectionWriteTransform sectionWriteTransform;
    MCSectionScheduleToEthosColleagueWriteTransform scheduleWriteTransform;
    MCSectionInstructorToEthosColleagueSectionInstructorWriteTransform sectionInstructorWriteTransform;
    MCSectionCrossListToEthosColleagueWriteTransform crossListWriteTransform;
    MCPersonEmergencyContactToEthosColleagueWriteTransform emergencyContactWriteTransform;
    MCPersonToEthosColleagueDupCheckWriteTransform personDuplicateWriteTransform;
    MCPersonToEthosColleaguePersonWriteTransform personWriteTransform;
    MCStudentChargeToEthosColleagueWriteTransform studentChargeWriteTransform;
    MCStudentPaymentToEthosColleagueWriteTransform studentPaymentWriteTransform;
    MCStudentEnrollmentToEthosColleagueWriteTransform studentEnrollmentWriteTransform;
    MCStudentToEthosColleagueWriteTransform studentWriteTransform;
    MCPersonAddressToEthosColleaguePersonAddressWriteTransform personAddressUpdateTransform;
    MCFinalGradeToEthosColleagueWriteTransform finalGradeWriteTransform;
    MCOrganizationToEthosColleagueWriteTransform organizationWriteTransform;

    @Override
    public String mapToCourseWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(courseWriteTransform, exchange, exchange.getMessage().getBody(MCCourse.class));
    }

    @Override
    public MCCourse mapFromCourseCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCCourse.class).transform(null, resp);
    }

    @Override
    public String mapToSectionWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(sectionWriteTransform, exchange, exchange.getMessage().getBody(MCSection.class));
    }

    @Override
    public MCSection mapFromSectionCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCSection.class).transform(null, resp);
    }

    @Override
    public String mapToSectionScheduleWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(scheduleWriteTransform, exchange,
                exchange.getMessage().getBody(MCSectionSchedule.class));
    }

    @Override
    public MCSectionSchedule mapFromSectionScheduleCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCSectionSchedule.class).transform(null, resp);
    }

    @Override
    public String mapToSectionInstructorAssignmentWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(sectionInstructorWriteTransform, exchange, exchange.getMessage().getBody(
                MCSectionInstructorAssignment.class));
    }

    @Override
    public MCSectionInstructorAssignment mapFromSectionInstructorAssignmentCreateResponse(String resp,
            Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCSectionInstructorAssignment.class).transform(null, resp);
    }

    @Override
    public String mapToSectionCrossListWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(crossListWriteTransform, exchange,
                exchange.getMessage().getBody(MCSectionCrossList.class));
    }

    @Override
    public MCSectionCrossList mapFromSectionCrossListCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCSectionCrossList.class).transform(null, resp);
    }

    @Override
    public String mapToPersonEmergencyContactWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(emergencyContactWriteTransform, exchange,
                exchange.getMessage().getBody(MCPersonEmergencyContact.class));
    }

    @Override
    public MCPersonEmergencyContact mapFromPersonEmergencyContactCreateResponse(String resp, Exchange exchange)
            throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCPersonEmergencyContact.class).transform(null, resp);
    }

    @Override
    public String mapToPersonWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(personWriteTransform, exchange, exchange.getMessage().getBody(MCPerson.class));
    }

    @Override
    public MCPerson mapFromPersonCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCPerson.class).transform(null, resp);
    }

    @Override
    public String mapToCheckDuplicatePersonWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(personDuplicateWriteTransform, exchange,
                exchange.getMessage().getBody(MCPerson.class));
    }

    @Override
    public MCStudentCharge mapFromStudentChargeCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCStudentCharge.class).transform(null, resp);
    }

    @Override
    public String mapToStudentChargeWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(studentChargeWriteTransform, exchange,
                exchange.getMessage().getBody(MCStudentCharge.class));
    }

    @Override
    public MCStudentPayment mapFromStudentPaymentCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCStudentPayment.class).transform(null, resp);
    }

    @Override
    public String mapToStudentPaymentWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(studentPaymentWriteTransform, exchange,
                exchange.getMessage().getBody(MCStudentPayment.class));
    }

    @Override
    public MCStudentEnrollment mapFromStudentEnrollmentCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCStudentEnrollment.class).transform(null, resp);
    }

    @Override
    public String mapToPersonAddressWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(personAddressUpdateTransform, exchange,
                exchange.getMessage().getBody(MCAddress.class));
    }

    @Override
    public String mapToStudentEnrollmentWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(studentEnrollmentWriteTransform, exchange,
                exchange.getMessage().getBody(MCStudentEnrollment.class));
    }

    @Override
    public String mapToStudentWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(studentWriteTransform, exchange,
                exchange.getMessage().getBody(MCStudent.class));
    }

    @Override
    public MCFinalGrade mapFromFinalGradeCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCFinalGrade.class).transform(null, resp);
    }

    @Override
    public String mapToFinalGradeWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(finalGradeWriteTransform, exchange, exchange.getMessage().getBody(MCFinalGrade.class));
    }

    @Override
    public MCSectionCrossListGroup mapFromSectionCrossListGroupCreateResponse(String resp, Exchange exchange)
            throws Exception {
        return null;
    }

    @Override
    public <T> T mapToSectionCrossListGroupWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public <T> T mapToCourseSectionInformationWriteRequest(Exchange exchange) throws Exception {
        return null;
    }

    @Override
    public MCOrganization mapFromOrganizationCreateResponse(String resp, Exchange exchange) throws Exception {
        return EthosHelper.ethosWriteRespTransformer(MCOrganization.class).transform(null, resp);
    }

    @Override
    public String mapToOrganizationWriteRequest(Exchange exchange) throws Exception {
        return invokeTransform(organizationWriteTransform, exchange, exchange.getMessage().getBody(MCOrganization.class));
    }

}
