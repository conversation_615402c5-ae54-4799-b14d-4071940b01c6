package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.ellucian.workflow.transform.ethos.BaseEthosSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueSectionScheduleSearchTransforms extends BaseEthosSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def sectionId = searchCriteria["sectionId"]
        if (sectionId) {
            queryParams["criteria"] = JsonOutput.toJson(["section": ["id": sectionId]])
        }

        def recurrenceStartOn = searchCriteria["recurrenceStartOn"]
        if (recurrenceStartOn) {
            queryParams["criteria"] = JsonOutput.toJson(["recurrence": ["timePeriod": ["startOn": recurrenceStartOn]]])
        }

        def recurrenceEndOn = searchCriteria["recurrenceEndOn"]
        if (recurrenceEndOn) {
            queryParams["criteria"] = JsonOutput.toJson(["recurrence": ["timePeriod": ["endOn": recurrenceEndOn]]])
        }

    }
}
