package org.moderncampus.integration.colleague.workflow.transform.section

import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.ellucian.workflow.transform.ethos.BaseEthosSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueSectionCrossListSearchTransforms extends BaseEthosSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def sectionId = searchCriteria["section"] as String
        if (sectionId) {
            queryParams["section"] = sectionId
        }
    }

}
