package org.moderncampus.integration.colleague.workflow.transform.person

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCPerson
import org.moderncampus.integration.ellucian.component.internal.ColleagueEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.ethos.EllucianCommonWriteTransform
import org.moderncampus.integration.helper.GroovyJsonSupport
import org.moderncampus.integration.transform.BaseTransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE
import static org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE
import static org.moderncampus.integration.constants.Constants.LOADED_ENTITY
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_UPDATE_PERSON
import static org.moderncampus.integration.transform.support.CommonFunctions.isValueExist

@Component
@CompileStatic
class MCPersonToEthosColleaguePersonWriteTransform extends BaseTransformer<MCPerson, String> {

    @Autowired
    EllucianCommonWriteTransform commonWriteTransform

    @Autowired
    MCCommonColleagueWriteTransform commonColleagueWriteTransform

    @Autowired
    MCPersonAddressToEthosColleaguePersonAddressWriteTransform personAddressToEthosColleaguePersonAddressWriteTransform

    @Override
    protected String doTransform(TransformContext ctx, MCPerson person) {
        Map<String, ?> ethosRequest = [:]

        def isUpdate = USE_CASE_UPDATE_PERSON == ctx.getContextProp(INTEGRATION_USE_CASE, String.class)

        def phonesTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.PHONE_TYPES)
        def addressTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.ADDRESS_TYPES)
        def emailTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.EMAIL_TYPES)
        def personNameTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.PERSON_NAME_TYPES)
        def citizenshipTypesById = commonWriteTransform.resourceMapFromContext(ctx, ColleagueEthosAPIResource.CITIZENSHIP_STATUSES)
        commonWriteTransform.mapEthosExtensions(ethosRequest, person)

        if (isUpdate) {
            ethosRequest = new JsonSlurper().parseText(ctx.getContextProp(LOADED_ENTITY, String.class)) as Map<String, Object>
        } else {
            commonWriteTransform.mapId(ethosRequest)
        }

        commonColleagueWriteTransform.mapRoles(isUpdate, ethosRequest, person.roles)
        commonColleagueWriteTransform.mapCredentials(isUpdate, ethosRequest, person.credentials)
        commonColleagueWriteTransform.mapNames(isUpdate, ethosRequest, person, personNameTypesById)
        commonColleagueWriteTransform.mapEmails(isUpdate, ethosRequest, person.emails, emailTypesById)
        commonColleagueWriteTransform.mapAlternativeCredentials(isUpdate, ethosRequest, person)
        commonColleagueWriteTransform.mapAddresses(ctx, isUpdate, ethosRequest, person.addresses, addressTypesById)
        commonColleagueWriteTransform.mapPhones(isUpdate, ethosRequest, person.phones, phonesTypesById)

        mapPersonFields(ethosRequest, person)
        mapEthnicity(ethosRequest, person)
        mapRaces(isUpdate, ethosRequest, person)

        mapCitizenshipStatus(ethosRequest, person, citizenshipTypesById)

        return JsonOutput.toJson(ethosRequest)
    }

    private void mapCitizenshipStatus(Map<String, Object> ethosRequest, MCPerson person, Map<String, Map> citizenshipTypesById) {
        if (!isValueExist(person.citizenshipStatus)) return

        def citizenshipStatusMap = citizenshipTypesById.find { it.value['id'] == person.citizenshipStatus }
        def categoryValue = citizenshipStatusMap.value['category']

        if (isValueExist(categoryValue)) {
            ethosRequest.citizenshipStatus = [
                    category: categoryValue,
                    detail  : [id: person.citizenshipStatus]
            ]
        }
    }

    private void mapPersonFields(Map<String, Object> ethosRequest, MCPerson person) {

        if (isValueExist(person.dateOfBirth)) {
            ethosRequest.dateOfBirth = ISO_LOCAL_DATE.format(person.dateOfBirth)
        }

        if (isValueExist(person.gender)) {
            ethosRequest.gender = person.gender
        }

        if (isValueExist(person.genderIdentity)) {
            commonWriteTransform.setNestedValue(ethosRequest, person.genderIdentity, "genderIdentity", "id")
        }

        if (isValueExist(person.pronouns)) {
            commonWriteTransform.setNestedValue(ethosRequest, person.pronouns, "personalPronoun", "id")
        }

        if (isValueExist(person.veteranStatus)) {
            commonWriteTransform.setNestedValue(ethosRequest, person.veteranStatus, "veteranStatus", "detail", "id")
        }

        if (isValueExist(person.citizenshipCountry)) {
            ethosRequest.citizenshipCountry = person.citizenshipCountry
        }
    }

    private void mapEthnicity(Map<String, Object> ethosRequest, MCPerson person) {
        if (isValueExist(person.ethnicity)) {
            commonWriteTransform.setNestedValue(ethosRequest, person.ethnicity.id, "ethnicity", "ethnicGroup", "id")
        }
    }

    private void mapRaces(boolean isUseCaseUpdate, Map<String, Object> ethosRequest, MCPerson person) {
        if (!isValueExist(person.races)) return

        GroovyJsonSupport.createOrUpdateExtEntityAssociation(this, isUseCaseUpdate, person.races, ethosRequest, 'races',
                (ethosRequest.races as List<Map<String, ?>>)?.groupBy { race -> return race['race']['id'] as String },
                'createOrUpdateRace', 'createOrUpdateRace', null, (personRace) -> personRace.id)
    }

    Map createOrUpdateRace(MCPerson.MCPersonRace personRace, boolean isUpdate, Map<String, ?> existingRace, Object args = null) {
        def raceMap = [:]

        if (isUpdate && existingRace != null) {
            raceMap = existingRace
        }
        raceMap.race = [id: personRace.id]
        return raceMap
    }

}
