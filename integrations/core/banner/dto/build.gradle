import org.springframework.boot.gradle.plugin.SpringBootPlugin

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {
    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    compileOnly project(':integrations:common:int-common-core')

    compileOnly 'org.hibernate.validator:hibernate-validator'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    testImplementation "org.junit.jupiter:junit-jupiter-api:$libJUnitJupiter"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$libJUnitJupiter"
}
test {
    useJUnitPlatform()
}