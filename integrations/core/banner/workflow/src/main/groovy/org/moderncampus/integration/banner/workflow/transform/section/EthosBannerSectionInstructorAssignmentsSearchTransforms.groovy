package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.banner.workflow.transform.common.search.BaseEthosBannerSearchTransforms
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosBannerSectionInstructorAssignmentsSearchTransforms extends BaseEthosBannerSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (!searchCriteria) return

        def section = searchCriteria["section"]
        if (section) {
            queryParams["criteria"] = JsonOutput.toJson(["section": section])
        }

        def instructor = searchCriteria["instructor"]
        if (instructor) {
            queryParams["criteria"] = JsonOutput.toJson(["instructor": instructor])
        }

        def sectionSchedule = searchCriteria["sectionSchedule"]
        if (sectionSchedule) {
            queryParams["criteria"] = JsonOutput.toJson(["instructionalEvents": sectionSchedule])
        }
    }
}
