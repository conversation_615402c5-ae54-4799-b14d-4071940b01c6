package org.moderncampus.integration.banner.workflow.transform.acadperiod

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.banner.workflow.transform.common.search.BaseEthosBannerSearchTransforms
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosAcademicPeriodSearchTransforms extends BaseEthosBannerSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {

        def useCase = exchange.getProperty(org.moderncampus.integration.constants.Constants.INTEGRATION_USE_CASE)
        if (EllucianConstants.USE_CASE_FILTER_YEARS == useCase) {
            queryParams["criteria"] = JsonOutput.toJson(["category": ["type": "year"]])
        }

        def searchOnStr = searchCriteria['startOn'] as String
        if (searchOnStr) {
            if (searchOnStr.contains('$gte')) {
                String value = searchOnStr.split('gte:')[1]
                queryParams["criteria"] = JsonOutput.toJson(["startOn": ['$gte': "${value}Z"]])
            } else {
                def value = searchOnStr
                if (searchOnStr.contains('$eq')) value = searchOnStr.split('eq:')[1]
                queryParams["criteria"] = JsonOutput.toJson(["startOn": "${value}Z"])
            }
        }

        def endOnStr = searchCriteria['endOn'] as String
        if (endOnStr) {
            if (endOnStr.contains('$lte')) {
                String value = endOnStr.split('gte:')[1]
                queryParams["criteria"] = JsonOutput.toJson(["endOn": ['lte': "${value}Z"]])
            } else {
                def value = endOnStr
                if (endOnStr.contains('$eq')) value = searchOnStr.split('lte:')[1]
                queryParams["criteria"] = JsonOutput.toJson(["endOn": "${value}Z"])
            }
        }

    }
}
