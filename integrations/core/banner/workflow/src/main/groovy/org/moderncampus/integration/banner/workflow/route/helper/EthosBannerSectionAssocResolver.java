package org.moderncampus.integration.banner.workflow.route.helper;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.V1_BANNER_GET_ACADEMIC_PERIOD;
import static org.moderncampus.integration.constants.Constants.QUERY_PARAMS;
import static org.moderncampus.integration.ellucian.component.internal.BannerBPAPIResource.COURSE_SECTION_INFORMATION;
import static org.moderncampus.integration.route.support.RouteSupport.routeQueryParamStr;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.camel.ProducerTemplate;
import org.moderncampus.integration.dto.core.MCAcademicPeriod;
import org.moderncampus.integration.dto.core.MCSection;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.route.dto.RouteExecutorResult;
import org.moderncampus.integration.route.executor.IRouteExecutor;
import org.moderncampus.integration.route.support.RouteSupport;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

@Component
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EthosBannerSectionAssocResolver extends BPAPIEthosAssocResolver implements Processor {

    IRouteExecutor routeExecutor;

    public EthosBannerSectionAssocResolver(ProducerTemplate template, CamelContext camelContext,
            EllucianEthosReadEndpointPaginator ellucianEthosReadEndpointPaginator, IRouteExecutor routeExecutor) {
        super(template, camelContext, ellucianEthosReadEndpointPaginator);
        this.routeExecutor = routeExecutor;
    }

    @Override
    public void process(Exchange exchange) throws Exception {
        RouteExecutorResult responseResult = exchange.getMessage().getBody(RouteExecutorResult.class);
        MCSection sectionResponse = (MCSection) responseResult.getResults();

        RouteExecutorResult academicPeriodResult = RouteSupport.executeRoute(routeExecutor,
                V1_BANNER_GET_ACADEMIC_PERIOD, sectionResponse.getReportingAcademicPeriod());
        MCAcademicPeriod academicPeriod = (MCAcademicPeriod) academicPeriodResult.getResults();
        String termCode = academicPeriod.getCode();
        String code = sectionResponse.getCode();

        Map<String, String> queryParameters = new HashMap<>();
        queryParameters.put("termCode", termCode);
        queryParameters.put("crn", code);

        Exchange newExchange = exchange.copy();
        newExchange.getMessage().setHeader(QUERY_PARAMS, routeQueryParamStr(queryParameters));
        List<Map<String, ?>> responses = invokeEthosReadEndpoint(COURSE_SECTION_INFORMATION, newExchange);
        Map<String, ?> stringMap = responses.getFirst();

        String xlstGroup = (String) stringMap.get("xlstGroup");
        if (xlstGroup != null) {
            MCSection.MCSectionCrossListGroup group = new MCSection.MCSectionCrossListGroup();
            group.setId(xlstGroup + "|" + termCode);
            sectionResponse.setCrossListGroup(group);
        }

    }
}
