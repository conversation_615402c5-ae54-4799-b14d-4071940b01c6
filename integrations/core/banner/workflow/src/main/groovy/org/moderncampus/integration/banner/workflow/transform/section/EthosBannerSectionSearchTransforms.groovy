package org.moderncampus.integration.banner.workflow.transform.section

import groovy.json.JsonOutput
import groovy.transform.CompileStatic
import org.apache.camel.Exchange
import org.moderncampus.integration.banner.workflow.route.Constants
import org.moderncampus.integration.banner.workflow.transform.common.search.BaseEthosBannerSearchTransforms
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Component
@CompileStatic
class EthosBannerSectionSearchTransforms extends BaseEthosBannerSearchTransforms {

    @Override
    void buildAdditionalCriteria(Exchange exchange, Map<String, String> queryParams, Map<String, Object> searchCriteria) {
        if (searchCriteria != null) {
            def criteriaMap = [:]
            if (searchCriteria['keyword']) {
                queryParams["keywordSearch"] = JsonOutput.toJson(["keywordSearch": searchCriteria['keyword']])
            }
            if (searchCriteria['code']) {
                criteriaMap["code"] = searchCriteria['code']
            }
            if (searchCriteria['number']) {
                criteriaMap["number"] = searchCriteria['number']
            }
            if (searchCriteria['startOn']) {
                criteriaMap["startOn"] = (searchCriteria['startOn'] as LocalDate).format(DateTimeFormatter.ISO_LOCAL_DATE)
            }
            if (searchCriteria['endOn']) {
                criteriaMap["endOn"] = (searchCriteria['endOn'] as LocalDate).format(DateTimeFormatter.ISO_LOCAL_DATE)
            }
            if (searchCriteria['academicPeriod']) {
                criteriaMap["academicPeriod"] = ["id": searchCriteria["academicPeriod"]]
            }
            if (searchCriteria['course']) {
                criteriaMap["course"] = ["id": searchCriteria["course"]]
            }
            if (searchCriteria['site']) {
                criteriaMap["site"] = ["id": searchCriteria["site"]]
            }
            if (searchCriteria['academicLevels']) {
                criteriaMap["academicLevels"] = searchCriteria["academicLevels"].collect {
                    ["id": it]
                }
            }
            if (searchCriteria['orgUnits']) {
                criteriaMap["owningInstitutionUnits"] = searchCriteria["orgUnits"].collect {
                    ["institutionUnit": ["id": it]]
                }
            }
            if (searchCriteria['status']) {
                Map<BannerEthosAPIResource, Map<String, Map>> ethosAssocCacheMap = exchange.getProperty(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
                Map<String, Map> sectionStatusMap = ethosAssocCacheMap[BannerEthosAPIResource.SECTION_STATUSES]
                String category = sectionStatusMap[searchCriteria['status']]?['category']
                if (category) {
                    criteriaMap["status"] = ["category": category]
                }
            }
            if (criteriaMap) {
                queryParams["criteria"] = JsonOutput.toJson(criteriaMap)
            }
        }
    }
}
