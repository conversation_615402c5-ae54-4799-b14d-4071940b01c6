package org.moderncampus.integration.banner.workflow.route.identifier;

import static org.moderncampus.integration.Constants.BANNER_SYSTEM_ID;
import static org.moderncampus.integration.Constants.VERSION_1;
import static org.moderncampus.integration.route.identifier.RouteIdSupport.generateRouteId;

import org.moderncampus.integration.route.identifier.Constants;
import org.moderncampus.integration.route.identifier.IRouteId;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum CoreBannerRouteIds implements IRouteId {

    V1_BANNER_GET_ORGANIZATIONAL_UNITS(Constants.GET_ORGANIZATIONAL_UNITS),
    V1_BANNER_GET_ORGANIZATIONAL_UNIT(Constants.GET_ORGANIZATIONAL_UNIT),
    V1_BANNER_GET_SUBJECTS(Constants.GET_SUBJECTS),
    V1_BANNER_GET_SUBJECT(Constants.GET_SUBJECT),
    V1_BANNER_GET_INSTRUCTIONAL_METHODS(Constants.GET_INSTRUCTIONAL_METHODS),
    V1_BANNER_GET_INSTRUCTIONAL_METHOD(Constants.GET_INSTRUCTIONAL_METHOD),
    V1_BANNER_GET_LOCATIONS(Constants.GET_LOCATIONS),
    V1_BANNER_GET_LOCATION(Constants.GET_LOCATION),
    V1_BANNER_GET_ACADEMIC_LEVELS(Constants.GET_ACADEMIC_LEVELS),
    V1_BANNER_GET_ACADEMIC_LEVEL(Constants.GET_ACADEMIC_LEVEL),
    V1_BANNER_GET_ROOMS(Constants.GET_ROOMS),
    V1_BANNER_GET_ROOM(Constants.GET_ROOM),
    V1_BANNER_GET_SECTIONS(Constants.GET_SECTIONS),
    V1_BANNER_GET_SECTION(Constants.GET_SECTION),
    V1_BANNER_CREATE_SECTION(Constants.CREATE_SECTION),
    V1_BANNER_UPDATE_SECTION(Constants.UPDATE_SECTION),
    V1_BANNER_CREATE_COURSE(Constants.CREATE_COURSE),
    V1_BANNER_UPDATE_COURSE(Constants.UPDATE_COURSE),
    V1_BANNER_GET_SECTION_SCHEDULE(Constants.GET_SECTION_SCHEDULE),
    V1_BANNER_GET_SECTION_SCHEDULES(Constants.GET_SECTION_SCHEDULES),
    V1_BANNER_CREATE_SECTION_SCHEDULE(Constants.CREATE_SECTION_SCHEDULE),
    V1_BANNER_UPDATE_SECTION_SCHEDULE(Constants.UPDATE_SECTION_SCHEDULE),
    V1_BANNER_DELETE_SECTION_SCHEDULE(Constants.DELETE_SECTION_SCHEDULE),
    V1_BANNER_GET_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.GET_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_BANNER_GET_SECTION_INSTRUCTOR_ASSIGNMENTS(Constants.GET_SECTION_INSTRUCTOR_ASSIGNMENTS),
    V1_BANNER_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.CREATE_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_BANNER_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_BANNER_DELETE_SECTION_INSTRUCTOR_ASSIGNMENT(Constants.DELETE_SECTION_INSTRUCTOR_ASSIGNMENT),
    V1_BANNER_GET_ACADEMIC_PERIODS(Constants.GET_ACADEMIC_PERIODS),
    V1_BANNER_GET_ACADEMIC_PERIOD(Constants.GET_ACADEMIC_PERIOD),
    V1_BANNER_GET_INSTRUCTORS(Constants.GET_INSTRUCTORS),
    V1_BANNER_GET_INSTRUCTOR(Constants.GET_INSTRUCTOR),
    V1_BANNER_GET_SECTION_CROSS_LIST_GROUPS(Constants.GET_SECTION_CROSS_LIST_GROUPS),
    V1_BANNER_CREATE_SECTION_CROSS_LIST_GROUP(Constants.CREATE_SECTION_CROSS_LIST_GROUP),
    V1_BANNER_UPDATE_SECTION_CROSS_LIST_GROUP(Constants.UPDATE_SECTION_CROSS_LIST_GROUP),
    V1_BANNER_UPDATE_COURSE_SECTION_INFORMATION(Constants.UPDATE_COURSE_SECTION_INFORMATION),
    V1_BANNER_GET_COURSE_SECTION_INFORMATION(Constants.GET_COURSE_SECTION_INFORMATION),
    V1_BANNER_PROXY(Constants.PROXY);

    String contextPath;

    String id;

    CoreBannerRouteIds(String contextPath) {
        this.contextPath = contextPath;
        this.id = generateRouteId(
                new String[]{VERSION_1, BANNER_SYSTEM_ID, contextPath});
    }
}
