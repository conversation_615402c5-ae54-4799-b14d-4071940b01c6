package org.moderncampus.integration.banner.workflow.route.beans.section;

import static org.moderncampus.integration.banner.workflow.route.identifier.CoreBannerRouteIds.*;
import static org.moderncampus.integration.banner.workflow.transform.EthosBannerWriteTransformer.*;
import static org.moderncampus.integration.ellucian.workflow.route.EllucianConstants.USE_CASE_FETCH_INSTRUCTORS;
import static org.moderncampus.integration.transform.IExtSystemReadTransformer.*;

import java.util.List;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.MulticastDefinition;
import org.moderncampus.integration.banner.workflow.route.helper.BPAPICrossListGroupsAssocHandler;
import org.moderncampus.integration.banner.workflow.route.helper.BannerEthosAssocResolver;
import org.moderncampus.integration.banner.workflow.route.helper.EthosBannerSectionAssocResolver;
import org.moderncampus.integration.banner.workflow.transform.BPAPIColleagueWriteTransformer;
import org.moderncampus.integration.banner.workflow.transform.EthosBannerReadTransformer;
import org.moderncampus.integration.banner.workflow.transform.EthosBannerWriteTransformer;
import org.moderncampus.integration.banner.workflow.transform.section.EthosBannerSectionCrossListGroupSearchTransforms;
import org.moderncampus.integration.banner.workflow.transform.section.EthosBannerSectionInstructorAssignmentsSearchTransforms;
import org.moderncampus.integration.banner.workflow.transform.section.EthosBannerSectionScheduleSearchTransforms;
import org.moderncampus.integration.banner.workflow.transform.section.EthosBannerSectionSearchTransforms;
import org.moderncampus.integration.ellucian.component.internal.BannerBPAPIResource;
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource;
import org.moderncampus.integration.ellucian.workflow.route.EllucianConstants;
import org.moderncampus.integration.ellucian.workflow.route.builder.banner.BannerEthosRouteBuilderSupport;
import org.moderncampus.integration.ellucian.workflow.route.builder.bpapi.BPAPIRouteBuilderSupport;
import org.moderncampus.integration.ellucian.workflow.route.helper.EllucianEthosReadEndpointPaginator;
import org.moderncampus.integration.route.support.DefaultListAggregationStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Configuration("bannerRouteBeanSectionDefinitions")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RouteBeanSectionDefinitions {

    EthosBannerReadTransformer readTransformer;

    EthosBannerWriteTransformer writeTransformer;

    BPAPIColleagueWriteTransformer bpapiColleagueWriteTransformer;

    BannerEthosAssocResolver bannerEthosAssocResolver;

    EllucianEthosReadEndpointPaginator readEPPaginator;

    ObjectMapper mapper;

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSectionInstructorAssignment() {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                BannerEthosAPIResource.SECTION_INSTRUCTORS, readTransformer,
                METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSectionInstructorAssignments(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosBannerSectionInstructorAssignmentsSearchTransforms sectionInstructorAssignmentsSearchTransforms) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_SECTION_INSTRUCTOR_ASSIGNMENTS.getId(),
                        BannerEthosAPIResource.SECTION_INSTRUCTORS, readTransformer,
                        METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT, aggregationStrategy
                )
                .searchCriteriaBuilder(sectionInstructorAssignmentsSearchTransforms).useCase(USE_CASE_FETCH_INSTRUCTORS)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerCreateSectionInstructorAssignment() {
        return BannerEthosRouteBuilderSupport.createBuilder(V1_BANNER_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                        BannerEthosAPIResource.SECTION_INSTRUCTORS, writeTransformer,
                        METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT_WRITE_REQUEST,
                        writeTransformer, METHOD_MAP_FROM_SECTION_INSTRUCTOR_ASSIGNMENT_CREATE_RESPONSE
                )
                .useCase(EllucianConstants.USE_CASE_CREATE_SECTION_INSTRUCTOR_ASSIGNMENT).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateSectionInstructorAssignment(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                        BannerEthosAPIResource.SECTION_INSTRUCTORS, writeTransformer,
                        METHOD_MAP_TO_SECTION_INSTRUCTOR_ASSIGNMENT_WRITE_REQUEST, mapper)
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION_INSTRUCTOR_ASSIGNMENT).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerDeleteSectionInstructorAssignment() {
        return BannerEthosRouteBuilderSupport.deleteByIdBuilder(V1_BANNER_DELETE_SECTION_INSTRUCTOR_ASSIGNMENT.getId(),
                BannerEthosAPIResource.SECTION_INSTRUCTORS,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerCreateSectionCrossListGroup() {
        return BPAPIRouteBuilderSupport.createBuilder(V1_BANNER_CREATE_SECTION_CROSS_LIST_GROUP.getId(),
                BannerBPAPIResource.SECTION_CROSS_LIST_GROUP, bpapiColleagueWriteTransformer,
                METHOD_MAP_TO_SECTION_CROSS_LIST_GROUP_WRITE_REQUEST, bpapiColleagueWriteTransformer,
                METHOD_MAP_FROM_SECTION_CROSS_LIST_GROUP_CREATE_RESPONSE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateSectionCrossListGroup() {
        return BPAPIRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_SECTION_CROSS_LIST_GROUP.getId(),
                        BannerBPAPIResource.SECTION_CROSS_LIST_GROUP, bpapiColleagueWriteTransformer,
                        METHOD_MAP_TO_SECTION_CROSS_LIST_GROUP_WRITE_REQUEST, mapper)
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION_CROSS_LIST_GROUP)
                .isPatchImpl(false)
                .isUpdateById(false)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSection(
            EthosBannerSectionAssocResolver sectionAssocResolver
    ) {
        return BannerEthosRouteBuilderSupport
                .getByIdBuilder(V1_BANNER_GET_SECTION.getId(),
                        BannerEthosAPIResource.SECTIONS, readTransformer,
                        METHOD_MAP_TO_SECTION
                )
                .associationResolver(bannerEthosAssocResolver)
                .associationPreFetchList(
                        List.of(BannerEthosAPIResource.SECTION_TITLE_TYPES,
                                BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                                BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
               .postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(sectionAssocResolver);
                }))
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerCreateSection(
            BPAPICrossListGroupsAssocHandler bPAPIcrossListGroupsAssocHandler) {
        return BannerEthosRouteBuilderSupport.createBuilder(V1_BANNER_CREATE_SECTION.getId(),
                        BannerEthosAPIResource.SECTIONS, writeTransformer,
                        METHOD_MAP_TO_SECTION_WRITE_REQUEST,
                        writeTransformer, METHOD_MAP_FROM_SECTION_CREATE_RESPONSE
                ).associationResolver(bannerEthosAssocResolver)
                .associationPreFetchList(List.of(BannerEthosAPIResource.SECTION_TITLE_TYPES,
                        BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                        BannerEthosAPIResource.CREDIT_CATEGORIES,
                        BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .useCase(EllucianConstants.USE_CASE_CREATE_SECTION)
                .postRouteActionBuilder((routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(bPAPIcrossListGroupsAssocHandler);
                })).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateSection(
            BPAPICrossListGroupsAssocHandler bPAPIcrossListGroupsAssocHandler
    ) {
        return BannerEthosRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_SECTION.getId(),
                        BannerEthosAPIResource.SECTIONS, writeTransformer, METHOD_MAP_TO_SECTION_WRITE_REQUEST, mapper)
                .associationResolver(bannerEthosAssocResolver)
                .associationPreFetchList(List.of(BannerEthosAPIResource.SECTION_TITLE_TYPES,
                        BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                        BannerEthosAPIResource.CREDIT_CATEGORIES,
                        BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS))
                .postRouteActionBuilder(routeDefinition -> {
                    MulticastDefinition multicastDefinition = routeDefinition.multicast().stopOnException()
                            .shareUnitOfWork();
                    multicastDefinition.bean(bPAPIcrossListGroupsAssocHandler);
                })
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSections(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosBannerSectionSearchTransforms sectionSearchTransforms) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_SECTIONS.getId(),
                        BannerEthosAPIResource.SECTIONS, readTransformer,
                        METHOD_MAP_TO_SECTION, aggregationStrategy
                )
                .searchCriteriaBuilder(sectionSearchTransforms)
                .associationResolver(bannerEthosAssocResolver).associationPreFetchList(
                        List.of(BannerEthosAPIResource.SECTION_TITLE_TYPES,
                                BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES,
                                BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS,
                                BannerEthosAPIResource.SECTION_STATUSES)).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerCreateSectionSchedule() {
        return BannerEthosRouteBuilderSupport.createBuilder(V1_BANNER_CREATE_SECTION_SCHEDULE.getId(),
                        BannerEthosAPIResource.INSTRUCTIONAL_EVENTS, writeTransformer,
                        METHOD_MAP_TO_SECTION_SCHEDULE_WRITE_REQUEST,
                        writeTransformer, METHOD_MAP_FROM_SECTION_SCHEDULE_CREATE_RESPONSE
                ).useCase(EllucianConstants.USE_CASE_CREATE_SECTION_SCHEDULE)
                .build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerUpdateSectionSchedule(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.updateByIdBuilder(V1_BANNER_UPDATE_SECTION_SCHEDULE.getId(),
                        BannerEthosAPIResource.INSTRUCTIONAL_EVENTS, writeTransformer,
                        METHOD_MAP_TO_SECTION_SCHEDULE_WRITE_REQUEST, mapper)
                .useCase(EllucianConstants.USE_CASE_UPDATE_SECTION_SCHEDULE).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSectionSchedule(
            DefaultListAggregationStrategy aggregationStrategy) {
        return BannerEthosRouteBuilderSupport.getByIdBuilder(V1_BANNER_GET_SECTION_SCHEDULE.getId(),
                BannerEthosAPIResource.INSTRUCTIONAL_EVENTS, readTransformer,
                METHOD_MAP_TO_SECTION_SCHEDULE
        ).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSectionSchedules(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosBannerSectionScheduleSearchTransforms sectionScheduleSearchTransforms) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_SECTION_SCHEDULES.getId(),
                        BannerEthosAPIResource.INSTRUCTIONAL_EVENTS, readTransformer,
                        METHOD_MAP_TO_SECTION_SCHEDULE, aggregationStrategy
                )
                .searchCriteriaBuilder(sectionScheduleSearchTransforms).build();
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerDeleteSectionSchedule() {
        return BannerEthosRouteBuilderSupport.deleteByIdBuilder(V1_BANNER_DELETE_SECTION_SCHEDULE.getId(),
                BannerEthosAPIResource.INSTRUCTIONAL_EVENTS,
                mapper);
    }

    @Bean
    @Lazy(value = false)
    public RouteBuilder v1BannerGetSectionCrossListGroups(
            DefaultListAggregationStrategy aggregationStrategy,
            EthosBannerSectionCrossListGroupSearchTransforms searchTransforms) {
        return BannerEthosRouteBuilderSupport.getAllBuilder(V1_BANNER_GET_SECTION_CROSS_LIST_GROUPS.getId(),
                        BannerEthosAPIResource.SECTION_CROSS_LIST_QUERY, readTransformer,
                        METHOD_MAP_TO_SECTION_CROSS_LIST_GROUPS,
                        aggregationStrategy
                )
                .searchCriteriaBuilder(searchTransforms).build();
    }

}
