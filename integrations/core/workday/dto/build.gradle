import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'com.github.bjornvester.wsdl2java' version '2.0.2'
    id 'com.github.bjornvester.xjc' version "1.8.2"
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {
    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    compileOnly project(':integrations:common:int-common-core')

    compileOnly "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"

    compileOnly 'org.hibernate.validator:hibernate-validator'

    testImplementation "org.junit.jupiter:junit-jupiter-api:$libJUnitJupiter"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$libJUnitJupiter"
}
test {
    useJUnitPlatform()
}

wsdl2java {
    includesWithOptions.set(["**/*_v41.0.*": Arrays.asList("-p", "workday.com.bsvc.v41")])
    generatedSourceDir.set(layout.projectDirectory.dir("src/main/java/org/moderncampus/integration/workday/dto/wsdl"))
}

xjc {
    outputJavaDir.set(layout.projectDirectory.dir("src/main/java/org/moderncampus/integration/workday/dto/xsd"))
}

tasks.findByName("xjc").enabled = false
tasks.findByName("wsdl2java").enabled = false