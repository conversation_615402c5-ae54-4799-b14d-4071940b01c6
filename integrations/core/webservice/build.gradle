import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

sourceSets {
    integrationTest {
        java {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
        }
    }
}

configurations {
    integrationTestImplementation.extendsFrom(testImplementation)
    integrationTestRuntimeOnly.extendsFrom(testRuntimeOnly)
}


configurations.configureEach {
    exclude group: "commons-logging", module: "commons-logging"
}



jar {
    enabled = false
}

dependencies {
    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    developmentOnly enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation "com.github.ben-manes.caffeine:caffeine:${libCaffeine}"
    implementation 'org.apache.commons:commons-lang3'
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"
    implementation project(':common:common-common')
    implementation project(':common:common-persistence')
    implementation project(':common:common-webservice-base')
    implementation project(':common:common-tenants')
    implementation project(':integrations:common:int-common-core')
    implementation project(':integrations:common:int-common-webservice')
    implementation project(':integrations:core:banner:int-core-banner-workflow')
    implementation project(':integrations:core:colleague:int-core-colleague-workflow')
    implementation project(':common:common-webservice-base')
    compileOnly project(':integrations:core:banner:int-core-banner-component')
    compileOnly project(':integrations:common:int-common-ellucian')
    implementation 'org.springframework.boot:spring-boot-starter-security'

    implementation "com.auth0:java-jwt:${libJavaJwt}"

    implementation "com.google.guava:guava:$libGuava"

    compileOnly 'org.projectlombok:lombok'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation 'org.jetbrains:annotations:26.0.2'

    integrationTestImplementation "org.wiremock:wiremock-standalone:${libWireMock}"
}

tasks.named('test') {
    useJUnitPlatform()
}

tasks.register('integrationTest', Test) {
    description = "Run integration tests"
    group = "verification"
    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    useJUnitPlatform()
}