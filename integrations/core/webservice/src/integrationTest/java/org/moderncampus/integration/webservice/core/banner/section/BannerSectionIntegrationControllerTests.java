package org.moderncampus.integration.webservice.core.banner.section;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.hamcrest.Matchers.matchesPattern;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.asString;
import static org.moderncampus.integration.webservice.core.IntegrationControllerTestsHelper.makeRequest;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;

@SpringBootTest
@TestPropertySource("/integration-test.properties")
@ActiveProfiles("dev")
@AutoConfigureMockMvc
public class BannerSectionIntegrationControllerTests {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    MockMvc mockMvc;

    @Value("${banner.bearer.token}")
    private String BANNER_BEARER_TOKEN;

    static final String GUID_PATTERN = "[a-zA-Z0-9]{8}(-[a-zA-Z0-9]{4}){3}-[a-zA-Z0-9]{12}";
    static final String BREADCRUMB_PATTERN = "[a-zA-Z0-9]{15}-[a-zA-Z0-9]{16}";

    @RegisterExtension
    static WireMockExtension wireMockServer = WireMockExtension.newInstance().options(wireMockConfig().dynamicPort())
            .build();

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("banner.cloud.integration.host",
                () -> String.format("localhost:%d", wireMockServer.getPort()));
        registry.add("banner.cloud.integration.auth.useHttp", () -> true);
        registry.add("banner.cloud.integration.useHttp", () -> true);
    }

    @BeforeEach
    void initTest(@Value("classpath:banner/ethos/EthosGetSectionTitleTypesResp.json") Resource ethosGetSectionTitleResp,
            @Value("classpath:banner/ethos/EthosGetCourseTitleTypesResp.json") Resource ethosGetCourseTitleTypesResp,
            @Value("classpath:banner/ethos/EthosGetSectionDescriptionTypesResp.json") Resource ethosGetSectionDescriptionTypesResp,
            @Value("classpath:banner/ethos/EthosGetAdminInstructionalMethodsResp.json") Resource ethosGetAdminInsMethodResp,
            @Value("classpath:banner/ethos/EthosGetSectionStatusesResp.json") Resource ethosGetSectionStatusesResp,
            @Value("classpath:banner/ethos/EthosGetCreditCategoriesResp.json") Resource ethosGetCreditCategoriesResp)
            throws Exception {

        String strippedToken = BANNER_BEARER_TOKEN.replaceAll("Bearer ", "");

        wireMockServer.stubFor(
                makeRequest(POST, ".*/auth", strippedToken, Map.of("Content-Type", "text/html; charset=utf-8")));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-title-types", ethosGetSectionTitleResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/course-title-types", ethosGetCourseTitleTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-description-types", ethosGetSectionDescriptionTypesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/administrative-instructional-methods", ethosGetAdminInsMethodResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/section-statuses", ethosGetSectionStatusesResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/credit-categories", ethosGetCreditCategoriesResp));
    }

    @Test
    void bannerGetSectionById(
            @Value("classpath:banner/ethos/EthosGetSectionByIdResp.json") Resource ethosGetSectionByIdResp,
            @Value("classpath:banner/ethos/EthosGetAcademicPeriodResp.json") Resource ethosGetAcademicPeriodResp,
            @Value("classpath:banner/bpapi/BPAPIGetCourseSectionInfoResp.json") Resource bpapiGetCourseInfoResp,
            @Value("classpath:banner/GetSingleSectionResp.json") Resource getSectionByIdResp) throws Exception {

        wireMockServer.stubFor(
                makeRequest(GET, ".*/sections/459893ae-88c8-4e12-9587-2e0037ad47d0", ethosGetSectionByIdResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/academic-periods/6493b482-ac98-4bf8-ac6f-54e2577c6386",
                ethosGetAcademicPeriodResp));
        wireMockServer.stubFor(
                makeRequest(GET, ".*/course-section-information.*", bpapiGetCourseInfoResp));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/sections/459893ae-88c8-4e12-9587-2e0037ad47d0").header(AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionByIdResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void bannerGetSections(@Value("classpath:banner/ethos/EthosGetSectionsResp.json") Resource ethosGetSectionsResp,
            @Value("classpath:banner/GetSectionsResp.json") Resource getSectionsResp) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/sections\\?.*", ethosGetSectionsResp));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/sections?pageOffset=0&pageSize=10").header(AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void bannerPostSection(@Value("classpath:banner/ethos/EthosPostSectionResp.json") Resource ethosPostSectionResp,
            @Value("classpath:banner/ethos/EthosPostSectionReq.json") Resource ethosPostSectionReq,
            @Value("classpath:banner/ethos/EthosGetAcademicPeriodResp.json") Resource ethosGetAcademicPeriodResp,
            @Value("classpath:banner/CreateSingleSectionReq.json") Resource createSingleSectionReq,
            @Value("classpath:banner/CreateSingleSectionResp.json") Resource createSingleSectionResp,
            @Value("classpath:banner/bpapi/BPAPIUpdateCourseSectionResp.json") Resource updateCourseSectionResp,
            @Value("classpath:banner/bpapi/BPAPIUpdateCourseSectionReq.json") Resource updateCourseSectionReq
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/sections", ethosPostSectionResp));
        wireMockServer.stubFor(makeRequest(GET, ".*/academic-periods/6493b482-ac98-4bf8-ac6f-54e2577c6386",
                ethosGetAcademicPeriodResp));
        wireMockServer.stubFor(makeRequest(PUT, ".*/course-section-information", updateCourseSectionResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/sections").contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(createSingleSectionReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSingleSectionResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/sections")).withRequestBody(
                equalToJson(asString(ethosPostSectionReq))));
        wireMockServer.verify(putRequestedFor(WireMock.urlMatching(".*/course-section-information")).withRequestBody(
                equalToJson(asString(updateCourseSectionReq))));
    }

    @Test
    void bannerPatchSection(@Value("classpath:banner/ethos/EthosPutSectionResp.json") Resource ethosPutSectionResp,
            @Value("classpath:banner/ethos/EthosGetForPutSectionResp.json") Resource ethosGetSectionResp,
            @Value("classpath:banner/ethos/EthosPutSectionReq.json") Resource ethosPutSectionReq,
            @Value("classpath:banner/ethos/EthosGetAcademicPeriodResp.json") Resource ethosGetAcademicPeriodResp,
            @Value("classpath:banner/UpdateSingleSectionReq.json") Resource updateSingleSectionReq,
            @Value("classpath:banner/bpapi/BPAPIUpdateCourseSectionResp.json") Resource updateCourseSectionResp,
            @Value("classpath:banner/UpdateSingleSectionResp.json") Resource updateSingleSectionResp,
            @Value("classpath:banner/bpapi/BPAPIUpdateCourseSectionReqSecUpdate.json") Resource updateCourseSectionReq) throws Exception {

        wireMockServer.stubFor(
                makeRequest(PUT, ".*/sections/bb078f2c-13a3-437c-ac40-612ffceef993", ethosPutSectionResp));

        wireMockServer.stubFor(
                makeRequest(GET, ".*/sections/bb078f2c-13a3-437c-ac40-612ffceef993", ethosGetSectionResp));

        wireMockServer.stubFor(makeRequest(GET, ".*/academic-periods/6493b482-ac98-4bf8-ac6f-54e2577c6386",
                ethosGetAcademicPeriodResp));

        wireMockServer.stubFor(makeRequest(PUT, ".*/course-section-information", updateCourseSectionResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.patch("/uapi/integration/v1/sections/bb078f2c-13a3-437c-ac40-612ffceef993")
                                .contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(updateSingleSectionReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSingleSectionResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/sections/bb078f2c-13a3-437c-ac40-612ffceef993")).withRequestBody(
                equalToJson(asString(ethosPutSectionReq))));

        wireMockServer.verify(putRequestedFor(WireMock.urlMatching(".*/course-section-information")).withRequestBody(
                equalToJson(asString(updateCourseSectionReq))));
    }

    @Test
    void bannerPostSectionInstructorAssignments(
            @Value("classpath:banner/ethos/EthosPostSectionInstructorResp.json") Resource ethosPostSectionInstructorResp,
            @Value("classpath:banner/ethos/EthosPostSectionInstructorReq.json") Resource ethosPostSectionInstructorReq,
            @Value("classpath:banner/CreateSingleSectionInstructorReq.json") Resource createSingleSectionInstructorReq,
            @Value("classpath:banner/CreateSingleSectionInstructorResp.json") Resource createSingleSectionInstructorResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/section-instructors", ethosPostSectionInstructorResp));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/section-instructor-assignments")
                                .contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(createSingleSectionInstructorReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSingleSectionInstructorResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/section-instructors")).withRequestBody(
                equalToJson(asString(ethosPostSectionInstructorReq))));
    }

    @Test
    void bannerPatchSectionInstructor(
            @Value("classpath:banner/ethos/EthosPutSectionInstructorResp.json") Resource ethosPutSectionInstructorResp,
            @Value("classpath:banner/ethos/EthosPostSectionInstructorResp.json") Resource ethosPostSectionInstructorResp,
            @Value("classpath:banner/ethos/EthosPutSectionInstructorReq.json") Resource ethosPutSectionInstructorReq,
            @Value("classpath:banner/UpdateSingleSectionInstructorReq.json") Resource updateSingleSectionInstructorReq,
            @Value("classpath:banner/UpdateSingleSectionInstructorResp.json") Resource updateSingleSectionInstructorResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(PUT, ".*/section-instructors/f0cc763d-2a3d-7588-f6b0-7600249986f2",
                ethosPutSectionInstructorResp));

        wireMockServer.stubFor(
                WireMock.get(WireMock.urlMatching(".*/section-instructors/f0cc763d-2a3d-7588-f6b0-7600249986f2"))
                        .willReturn(aResponse().withHeader("Content-Type", "application/json")
                                .withBody(asString(ethosPostSectionInstructorResp))));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.patch(
                                        "/uapi/integration/v1/section-instructor-assignments/f0cc763d-2a3d-7588-f6b0-7600249986f2")
                                .contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(updateSingleSectionInstructorReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSingleSectionInstructorResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/section-instructors/f0cc763d-2a3d-7588-f6b0-7600249986f2")).withRequestBody(
                equalToJson(asString(ethosPutSectionInstructorReq))));
    }

    @Test
    void bannerDeleteSectionInstructor(@Value("classpath:banner/DeleteSectionInstructorResp.json") Resource apiResponse)
            throws Exception {

        wireMockServer.stubFor(
                delete(urlMatching(".*/section-instructors/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );

        this.mockMvc.perform(
                        MockMvcRequestBuilders.delete(
                                        "/uapi/integration/v1/section-instructor-assignments/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN)
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(apiResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/section-instructors/09352284-8e81-4a6a-add8-4bcfeae126e1")));
    }

    @Test
    void bannerSectionScheduleById(
            @Value("classpath:banner/ethos/EthosGetInstructionalEventsByIdResp.json") Resource ethosGetInstructionalEventsBy,
            @Value("classpath:banner/GetSingleSectionScheduleResp.json") Resource getSectionScheduleByIdResp)
            throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1",
                ethosGetInstructionalEventsBy));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/section-schedules/09352284-8e81-4a6a-add8-4bcfeae126e1").header(
                                AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionScheduleByIdResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

    }

    @Test
    void bannerSectionScheduleBadRequestMoreThanOneParameter() throws Exception {
        this.mockMvc.perform(
                        get("/uapi/integration/v1/section-schedules?sectionId=some-uuid&recurrenceStartOn=2025-02-25").header(
                                AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    @Test
    void bannerSectionScheduleSearchBySection(
            @Value("classpath:banner/ethos/EthosGetInstructionalEventsBySearchResp.json") Resource ethosGetInstructionalEventsBySearch,
            @Value("classpath:banner/GetSearchSectionScheduleResp.json") Resource getSectionScheduleBySearchResponse)
            throws Exception {
        String sectionUuid = "09352284-8e81-4a6a-add8-4bcfeae126e1";

        wireMockServer.stubFor(makeRequest(GET, ".*/instructional-events.*", ethosGetInstructionalEventsBySearch));

        this.mockMvc.perform(
                        get("/uapi/integration/v1/section-schedules?sectionId=" + sectionUuid).header(
                                AUTHORIZATION,
                                BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionScheduleBySearchResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }


    @Test
    void bannerPostSectionSchedule(
            @Value("classpath:banner/ethos/EthosPostInstructionalEventReq.json") Resource ethosRequest,
            @Value("classpath:banner/ethos/EthosPostInstructionalEventResp.json") Resource ethosResponse,
            @Value("classpath:banner/CreateSectionScheduleReq.json") Resource apiRequest
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/instructional-events", ethosResponse,
                Map.of(CONTENT_TYPE, "application/vnd.hedtech.integration.v11.1.0+json")));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.post("/uapi/integration/v1/section-schedules")
                                .contentType("application/vnd.hedtech.integration.v11.1.0+json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN)
                                .content(asString(apiRequest)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").value(matchesPattern(BREADCRUMB_PATTERN)))
                .andExpect(jsonPath("$.data.id").isNotEmpty())
                .andExpect(jsonPath("$.data.id").value(matchesPattern(GUID_PATTERN)));

        wireMockServer.verify(
                postRequestedFor(WireMock.urlMatching(".*/instructional-events"))
                        //.withHeader(ACCEPT, equalTo("application/vnd.hedtech.integration.v11.1.0+json"))
                        //.withHeader(CONTENT_TYPE, equalTo("application/vnd.hedtech.integration.v11.1.0+json"))
                        //.withHeader(AUTHORIZATION, containing("Bearer"))
                        .withRequestBody(equalToJson(asString(ethosRequest))));
    }

    @Test
    void bannerPatchSectionSchedule(@Value("classpath:banner/ethos/EthosPutInstructionalEventsResp.json") Resource ethosPutSectionScheduleResp,
            @Value("classpath:banner/ethos/EthosGetForPutInstructionalEventsResp.json") Resource ethosGetSectionScheduleResp,
            @Value("classpath:banner/ethos/EthosPutInstructionalEventsReq.json") Resource ethosPutSectionScheduleReq,
            @Value("classpath:banner/UpdateSectionScheduleReq.json") Resource updateSingleSectionScheduleReq,
            @Value("classpath:banner/UpdateSectionScheduleResp.json") Resource updateSingleSectionScheduleResp) throws Exception {

        wireMockServer.stubFor(WireMock.put(WireMock.urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                .willReturn(aResponse().withHeader("Content-Type", "application/json")
                        .withBody(asString(ethosPutSectionScheduleResp))));

        wireMockServer.stubFor(WireMock.get(WireMock.urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                .willReturn(aResponse().withHeader("Content-Type", "application/json")
                        .withBody(asString(ethosGetSectionScheduleResp))));

        this.mockMvc.perform(
                        MockMvcRequestBuilders.patch("/uapi/integration/v1/section-schedules/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .contentType("application/json")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(updateSingleSectionScheduleReq)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSingleSectionScheduleResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(putRequestedFor(
                WireMock.urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1")).withRequestBody(
                equalToJson(asString(ethosPutSectionScheduleReq))));
    }



    @Test
    void bannerDeleteSectionSchedule(@Value("classpath:banner/DeleteSectionScheduleResp.json") Resource apiResponse)
            throws Exception {

        wireMockServer.stubFor(
                delete(urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1"))
                        .willReturn(
                                aResponse()
                                        .withStatus(HttpStatus.NO_CONTENT.value())
                                        .withHeader("Content-Type", "application/json")
                        )
        );

        this.mockMvc.perform(
                        MockMvcRequestBuilders.delete("/uapi/integration/v1/section-schedules/09352284-8e81-4a6a-add8-4bcfeae126e1")
                                .header(AUTHORIZATION, BANNER_BEARER_TOKEN)
                )
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(apiResponse)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(deleteRequestedFor(
                WireMock.urlMatching(".*/instructional-events/09352284-8e81-4a6a-add8-4bcfeae126e1")));
    }

    @Test
    void bannerGetSectionInstructorAssignment(
            @Value("classpath:banner/ethos/EthosGetSectionInstructorsAssignmentResp.json") Resource ethosGetSectionInstructorsResp,
            @Value("classpath:banner/GetSectionInstructorsAssignmentResp.json") Resource getSectionInstructorsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/section-instructors/e3384e61-3025-4a93-b865-698948ea7ad6", ethosGetSectionInstructorsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/section-instructor-assignments/e3384e61-3025-4a93-b865-698948ea7ad6")
                        .contentType("application/json")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionInstructorsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

    }

    @Test
    void bannerGetSectionInstructorAssignments(
            @Value("classpath:banner/ethos/EthosGetSectionInstructors.json") Resource ethosGetSectionInstructorsResp,
            @Value("classpath:banner/GetSectionInstructorsResp.json") Resource getSectionInstructorsResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/section-instructors", ethosGetSectionInstructorsResp,
                Map.of("Content-Type", "application/vnd.hedtech.integration.v10+json")));

        this.mockMvc.perform(MockMvcRequestBuilders.get("/uapi/integration/v1/section-instructor-assignments")
                        .contentType("application/json")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(getSectionInstructorsResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

    @Test
    void colleaguePostSectionCrossListGroup(
            @Value("classpath:banner/ethos/EthosPostSectionCrossListGroupResp.json") Resource ethosPostSectionCrossListGroupResp,
            @Value("classpath:banner/ethos/EthosPostSectionCrossListGroupReq.json") Resource ethosPostSectionCrossListGroupReq,
            @Value("classpath:banner/CreateSectionCrossListGroupReq.json") Resource createSectionCrossListGroupReq,
            @Value("classpath:banner/CreateSectionCrossListGroupResp.json") Resource createSectionCrossListGroupResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(POST, ".*/schedule-cross-list-definition", ethosPostSectionCrossListGroupResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .post("/uapi/integration/v1/section-cross-list-groups")
                        .contentType("application/json")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(createSectionCrossListGroupReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSectionCrossListGroupResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(postRequestedFor(WireMock.urlMatching(".*/schedule-cross-list-definition")).withRequestBody(
                equalToJson(asString(ethosPostSectionCrossListGroupReq))));
    }

    @Test
    void colleaguePutSectionCrossListGroup(
            @Value("classpath:banner/bpapi/BPAPIPutSectionCrossListGroupResp.json") Resource ethosPutSectionCrossListGroupResp,
            @Value("classpath:banner/bpapi/BPAPIPutSectionCrossListGroupReq.json") Resource ethosPutSectionCrossListGroupReq,
            @Value("classpath:banner/UpdateSectionCrossListGroupReq.json") Resource updateSectionCrossListGroupReq,
            @Value("classpath:banner/UpdateSectionCrossListGroupResp.json") Resource updateSectionCrossListGroupResp
    ) throws Exception {

        wireMockServer.stubFor(
                makeRequest(PUT, ".*/schedule-cross-list-definition", ethosPutSectionCrossListGroupResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .put("/uapi/integration/v1/section-cross-list-groups/A3|202520")
                        .contentType("application/json")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN).content(asString(updateSectionCrossListGroupReq))
                ).andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(updateSectionCrossListGroupResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());

        wireMockServer.verify(
                putRequestedFor(WireMock.urlMatching(".*/schedule-cross-list-definition")).withRequestBody(
                        equalToJson(asString(ethosPutSectionCrossListGroupReq))));
    }


    @Test
    void colleagueGetSectionCrossListGroups(
            @Value("classpath:banner/ethos/EthosCrossListQueryResp.json") Resource ethosCrossListQueryResp,
            @Value("classpath:banner/GetSectionCrossListGroupResp.json") Resource createSectionCrossListGroupResp
    ) throws Exception {

        wireMockServer.stubFor(makeRequest(GET, ".*/schedule-cross-list-query", ethosCrossListQueryResp));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/uapi/integration/v1/section-cross-list-groups")
                        .contentType("application/json")
                        .header(AUTHORIZATION, BANNER_BEARER_TOKEN))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(content().json(asString(createSectionCrossListGroupResp)))
                .andExpect(jsonPath("$.meta.metaProps.breadcrumbId").exists());
    }

}
