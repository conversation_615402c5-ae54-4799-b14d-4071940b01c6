import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

sourceSets {
    integrationTest {
        java {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
        }
    }
}

configurations {
    integrationTestImplementation.extendsFrom(testImplementation)
    integrationTestRuntimeOnly.extendsFrom(testRuntimeOnly)
}


configurations.configureEach {
    exclude group: "commons-logging", module: "commons-logging"
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

jar {
    enabled = false
}

dependencies {
    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    developmentOnly enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation "com.github.ben-manes.caffeine:caffeine:${libCaffeine}"
    implementation 'org.apache.commons:commons-lang3'
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"
    implementation project(':integrations:custom:workday:int-custom-workday-workflow')
    implementation project(':integrations:core:workday:int-core-workday-dto')
    implementation project(':common:common-common')
    implementation project(':integrations:common:int-common-core')
    implementation project(':integrations:common:int-common-webservice')
    implementation project(':integrations:common:int-common-custom-webservice')
    implementation project(':common:common-webservice-base')
    compileOnly project(':common:common-tenants')
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'

    compileOnly 'org.projectlombok:lombok'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"

    integrationTestImplementation "org.wiremock:wiremock-standalone:${libWireMock}"
}

tasks.named('test') {
    useJUnitPlatform()
}

tasks.register('integrationTest', Test) {
    description = "Run integration tests"
    group = "verification"
    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    useJUnitPlatform()
}