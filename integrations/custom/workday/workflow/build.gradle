import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id "groovy"
}

sourceSets.main.java.srcDirs += ['src/customRoutes/java']


sourceSets {
    customTransform {
        java {
            srcDirs = ['src/customTransform/java']
        }
    }
}
// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    implementation enforcedPlatform("org.apache.camel.springboot:camel-spring-boot-bom:${libCamel}")

    implementation 'org.springframework:spring-context'

    implementation project(':common:common-common')
    implementation project(':integrations:common:int-common-core')
    implementation project(':integrations:core:workday:int-core-workday-dto')
    implementation project(':integrations:core:workday:int-core-workday-component')

    implementation 'org.apache.camel.springboot:camel-spring-boot-starter'
    implementation 'org.apache.camel.springboot:camel-console-starter'
    implementation 'org.apache.camel.springboot:camel-stream-starter'
    implementation 'org.apache.camel.springboot:camel-bean-validator-starter'
    implementation 'org.apache.camel.springboot:camel-jackson-starter'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    implementation 'org.apache.camel.springboot:camel-groovy-starter'
    implementation 'org.apache.groovy:groovy-xml'

    implementation "com.googlecode.libphonenumber:libphonenumber:${libPhoneNumber}"

    implementation "org.apache.commons:commons-collections4:${libCollections4}"

    implementation "org.apache.commons:commons-lang3"
    implementation "commons-io:commons-io:${libCommonsIo}"

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    customTransformCompileOnly sourceSets.main.compileClasspath + sourceSets.main.output

    testImplementation "org.springframework:spring-web"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$libJUnitJupiter"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "org.apache.camel:camel-test-spring-junit5:${libCamel}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$libJUnitJupiter"
}

compileGroovy {
    options.encoding = compileJava.options.encoding
    options.compilerArgs.addAll(compileJava.options.compilerArgs)
}

test {
    useJUnitPlatform()
}
