import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

tasks.named("jar") {
    archiveClassifier = ''
}

sourceSets {
    integrationTest {
        java {
            compileClasspath += main.output + test.output
            runtimeClasspath += main.output + test.output
        }
    }
}

configurations {
    integrationTestImplementation.extendsFrom(testImplementation)
    integrationTestRuntimeOnly.extendsFrom(testRuntimeOnly)
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.session:spring-session-core'

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"

    implementation project(':common:common-common')
    implementation project(':integrations:common:int-common-core')
    implementation project(':common:common-webservice-base')
    implementation project(':integrations:common:int-common-webservice')
    implementation project(':integrations:common:int-common-custom-webservice')
    implementation project(':integrations:custom:peoplesoft:int-custom-peoplesoft-destiny-tmu-workflow')
    compileOnly project(':integrations:custom:peoplesoft:int-custom-peoplesoft-destiny-tmu-component')
    compileOnly project(':common:common-tenants')

    implementation 'org.apache.commons:commons-lang3'

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"

}

tasks.register('integrationTest', Test) {
    description = "Run integration tests"
    group = "verification"
    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
    useJUnitPlatform()
}