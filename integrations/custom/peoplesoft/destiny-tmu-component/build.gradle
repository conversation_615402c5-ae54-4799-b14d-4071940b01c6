import org.springframework.boot.gradle.plugin.SpringBootPlugin

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    implementation enforcedPlatform("org.apache.camel.springboot:camel-spring-boot-bom:${libCamel}")

    implementation 'org.springframework:spring-context'

    implementation project(':common:common-common')
    implementation project(':integrations:common:int-common-core')

    implementation 'org.apache.camel.springboot:camel-spring-boot-starter'
    implementation 'org.apache.camel.springboot:camel-console-starter'
    implementation 'org.apache.camel.springboot:camel-stream-starter'
    implementation 'org.apache.camel.springboot:camel-http-starter'
    implementation 'org.apache.camel.springboot:camel-jaxb-starter'
    implementation 'org.apache.camel.springboot:camel-jackson-starter'

    implementation 'org.apache.camel.springboot:camel-cxf-soap-starter'
    implementation "org.apache.camel:camel-cxf-spring-soap:${libCamel}"

    implementation "com.auth0:java-jwt:${libJavaJwt}"

    compileOnly "org.apache.httpcomponents.client5:httpclient5"

    implementation('com.fasterxml.jackson.core:jackson-annotations')
    implementation('com.fasterxml.jackson.core:jackson-databind')
    implementation "org.apache.commons:commons-lang3"

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation "org.springframework:spring-web"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$libJUnitJupiter"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "org.apache.camel:camel-test-spring-junit5:${libCamel}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$libJUnitJupiter"
}

test {
    useJUnitPlatform()
}
