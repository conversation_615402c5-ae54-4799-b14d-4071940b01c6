import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'org.springframework.boot'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

configurations.configureEach {
    exclude group: "commons-logging", module: "commons-logging"
}

bootRun {
    if (project.hasProperty('dev')) {
        jvmArgs = ['-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005', '-Dspring.profiles.active=dev']
    }
}

jar {
    enabled = false
}

// Configure duplicate handling for all jar tasks
tasks.withType(Jar) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {
    implementation enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    developmentOnly enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-authorization-server'
    implementation 'org.apache.commons:commons-lang3'
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${libSpringDoc}"
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation project(':common:common-common')
    implementation project(':common:common-persistence')
    implementation project(':common:common-tenants')
    implementation project(':common:common-webservice-base')

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    annotationProcessor enforcedPlatform(SpringBootPlugin.BOM_COORDINATES)
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "junit:junit:${libJUnitJupiter}"
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation "org.testcontainers:testcontainers:${libTestContainers}"
    testImplementation "org.testcontainers:localstack:${libTestContainers}"
}

tasks.named('test') {
    useJUnitPlatform()
}